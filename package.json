{"name": "oneclass-platform", "version": "1.0.0", "description": "OneClass Platform - Educational SaaS Platform", "main": "index.js", "scripts": {"dev": "concurrently \"npm run dev:frontend\" \"npm run dev:backend\"", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && python main.py", "start": "npm run dev", "build": "cd frontend && npm run build", "test": "npm run test:frontend && npm run test:backend", "test:frontend": "cd frontend && npm test", "test:backend": "cd backend && python -m pytest"}, "dependencies": {"@tanstack/react-query": "^5.0.0", "@tanstack/react-query-devtools": "^5.0.0", "axios": "^1.6.0", "date-fns": "^3.0.0", "lodash": "^4.17.21", "react": "^18.2.0"}, "devDependencies": {"concurrently": "^8.2.0", "nodemon": "^3.0.0"}, "keywords": ["education", "saas", "platform", "react", "python"], "author": "", "license": "ISC"}